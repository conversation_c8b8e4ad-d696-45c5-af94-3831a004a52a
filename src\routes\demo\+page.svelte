<script>
	import { SvelteFlow, Controls, Background, MiniMap } from '@xyflow/svelte';
	import '@xyflow/svelte/dist/style.css';
	
	// Import demo node components
	import InputNode from '$lib/components/demo/InputNode.svelte';
	import SelectNode from '$lib/components/demo/SelectNode.svelte';
	import SwitchNode from '$lib/components/demo/SwitchNode.svelte';
	import CheckboxNode from '$lib/components/demo/CheckboxNode.svelte';
	import RadioGroupNode from '$lib/components/demo/RadioGroupNode.svelte';
	import SliderNode from '$lib/components/demo/SliderNode.svelte';
	
	import { Card, CardContent, CardHeader, CardTitle } from '$lib/components/ui/card';
	import { Button } from '$lib/components/ui/button';
	
	// Define node types
	const nodeTypes = {
		input: InputNode,
		select: SelectNode,
		switch: SwitchNode,
		checkbox: CheckboxNode,
		radioGroup: RadioGroupNode,
		slider: SliderNode
	};

	// Initial nodes with different field types
	let nodes = $state([
		{
			id: '1',
			type: 'input',
			position: { x: 100, y: 100 },
			data: {
				label: 'User Name',
				placeholder: 'Enter your name...',
				value: ''
			}
		},
		{
			id: '2',
			type: 'select',
			position: { x: 400, y: 100 },
			data: {
				label: 'Game Mode',
				options: [
					{ value: 'easy', label: 'Easy Mode' },
					{ value: 'normal', label: 'Normal Mode' },
					{ value: 'hard', label: 'Hard Mode' },
					{ value: 'expert', label: 'Expert Mode' }
				],
				value: ''
			}
		},
		{
			id: '3',
			type: 'switch',
			position: { x: 700, y: 100 },
			data: {
				label: 'Audio Settings',
				switchLabel: 'Enable sound effects',
				value: true
			}
		},
		{
			id: '4',
			type: 'checkbox',
			position: { x: 100, y: 300 },
			data: {
				label: 'Features',
				options: [
					{ id: 'hints', label: 'Show hints' },
					{ id: 'timer', label: 'Show timer' },
					{ id: 'score', label: 'Show score' },
					{ id: 'leaderboard', label: 'Show leaderboard' }
				],
				values: { hints: true, timer: true }
			}
		},
		{
			id: '5',
			type: 'radioGroup',
			position: { x: 400, y: 300 },
			data: {
				label: 'Difficulty Level',
				options: [
					{ value: 'beginner', label: 'Beginner' },
					{ value: 'intermediate', label: 'Intermediate' },
					{ value: 'advanced', label: 'Advanced' }
				],
				value: 'intermediate'
			}
		},
		{
			id: '6',
			type: 'slider',
			position: { x: 700, y: 300 },
			data: {
				label: 'Volume Control',
				description: 'Set the audio volume:',
				value: 75,
				min: 0,
				max: 100,
				step: 5,
				range: false
			}
		},
		{
			id: '7',
			type: 'slider',
			position: { x: 100, y: 500 },
			data: {
				label: 'Score Range',
				description: 'Set minimum and maximum scores:',
				values: [20, 80],
				min: 0,
				max: 100,
				step: 10,
				range: true
			}
		}
	]);

	// Initial edges connecting some nodes
	let edges = $state([
		{
			id: 'e1-2',
			source: '1',
			target: '2',
			animated: true,
			style: 'stroke: #3b82f6; stroke-width: 2px;'
		},
		{
			id: 'e2-3',
			source: '2',
			target: '3',
			animated: true,
			style: 'stroke: #10b981; stroke-width: 2px;'
		},
		{
			id: 'e4-5',
			source: '4',
			target: '5',
			animated: true,
			style: 'stroke: #f59e0b; stroke-width: 2px;'
		},
		{
			id: 'e5-6',
			source: '5',
			target: '6',
			animated: true,
			style: 'stroke: #8b5cf6; stroke-width: 2px;'
		}
	]);

	// Handle node drag stop
	function onNodeDragStop(event) {
		console.log('Node dragged:', event.detail);
	}

	// Handle node click
	function onNodeClick(event) {
		console.log('Node clicked:', event.detail);
	}

	// Reset all values
	function resetValues() {
		nodes = nodes.map(node => ({
			...node,
			data: {
				...node.data,
				value: node.type === 'switch' ? false : (node.type === 'slider' && !node.data.range ? 50 : ''),
				values: node.type === 'checkbox' ? {} : (node.type === 'slider' && node.data.range ? [25, 75] : undefined)
			}
		}));
	}

	// Log current values
	function logValues() {
		const values = {};
		nodes.forEach(node => {
			values[node.id] = {
				type: node.type,
				label: node.data.label,
				value: node.data.value,
				values: node.data.values
			};
		});
		console.log('Current node values:', values);
		alert('Check the console for current values!');
	}
</script>

<svelte:head>
	<title>Node Demo - AMQ PLUS</title>
	<meta name="description" content="Interactive demo of various node types with different input fields" />
</svelte:head>

<div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-4">
	<div class="mx-auto max-w-7xl">
		<!-- Header -->
		<Card class="mb-6 border-2 border-indigo-200 bg-white/90 backdrop-blur-sm">
			<CardHeader>
				<CardTitle class="text-center text-3xl font-bold text-indigo-900">
					🎛️ Interactive Node Demo
				</CardTitle>
				<p class="text-center text-indigo-700">
					Explore different types of interactive nodes with various input fields
				</p>
			</CardHeader>
			<CardContent>
				<div class="flex justify-center gap-4">
					<Button onclick={resetValues} variant="outline">
						🔄 Reset Values
					</Button>
					<Button onclick={logValues}>
						📊 Log Values
					</Button>
				</div>
			</CardContent>
		</Card>

		<!-- Flow Container -->
		<Card class="border-2 border-indigo-200 bg-white/95 backdrop-blur-sm">
			<CardContent class="p-0">
				<div class="h-[600px] w-full">
					<SvelteFlow
						{nodes}
						{edges}
						{nodeTypes}
						onnodedragstop={onNodeDragStop}
						onnodeclick={onNodeClick}
						fitView
						proOptions={{ hideAttribution: true }}
					>
						<Background variant="dots" gap={20} size={1} color="rgba(99, 102, 241, 0.1)" />
						<Controls />
						<MiniMap />
					</SvelteFlow>
				</div>
			</CardContent>
		</Card>

		<!-- Instructions -->
		<Card class="mt-6 border-2 border-indigo-200 bg-white/90 backdrop-blur-sm">
			<CardHeader>
				<CardTitle class="text-lg font-semibold text-indigo-900">
					📋 Demo Instructions
				</CardTitle>
			</CardHeader>
			<CardContent>
				<div class="grid gap-4 md:grid-cols-2">
					<div>
						<h3 class="font-semibold text-indigo-800 mb-2">Node Types:</h3>
						<ul class="space-y-1 text-sm text-indigo-700">
							<li>📝 <strong>Input Node:</strong> Text input field</li>
							<li>📋 <strong>Select Node:</strong> Dropdown selection</li>
							<li>🔄 <strong>Switch Node:</strong> Toggle switch</li>
							<li>☑️ <strong>Checkbox Node:</strong> Multiple checkboxes</li>
							<li>🔘 <strong>Radio Group:</strong> Single selection from group</li>
							<li>🎚️ <strong>Slider Nodes:</strong> Single value and range sliders</li>
						</ul>
					</div>
					<div>
						<h3 class="font-semibold text-indigo-800 mb-2">Interactions:</h3>
						<ul class="space-y-1 text-sm text-indigo-700">
							<li>• Drag nodes to reposition them</li>
							<li>• Interact with the input fields in each node</li>
							<li>• Use the controls to zoom and pan</li>
							<li>• Click "Log Values" to see current values</li>
							<li>• Click "Reset Values" to clear all inputs</li>
						</ul>
					</div>
				</div>
			</CardContent>
		</Card>
	</div>
</div>

<style>
	:global(.svelte-flow__node) {
		font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
	}

	:global(.svelte-flow__controls) {
		background: rgba(255, 255, 255, 0.9);
		border: 1px solid rgba(99, 102, 241, 0.2);
		border-radius: 8px;
	}

	:global(.svelte-flow__controls button) {
		background: rgba(99, 102, 241, 0.1);
		border: none;
		color: #6366f1;
		transition: all 0.2s ease;
	}

	:global(.svelte-flow__controls button:hover) {
		background: rgba(99, 102, 241, 0.2);
		transform: scale(1.05);
	}

	:global(.svelte-flow__minimap) {
		background: rgba(255, 255, 255, 0.9);
		border: 1px solid rgba(99, 102, 241, 0.2);
		border-radius: 8px;
	}
</style>
