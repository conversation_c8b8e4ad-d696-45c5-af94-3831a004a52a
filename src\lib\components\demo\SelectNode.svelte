<script>
	import { Handle, Position } from '@xyflow/svelte';
	import { Card, CardContent, CardHeader, CardTitle } from '$lib/components/ui/card';
	import * as Select from '$lib/components/ui/select';
	import { Label } from '$lib/components/ui/label';

	let { data } = $props();
	let selectedValue = $state(data.value || '');

	const options = data.options || [
		{ value: 'option1', label: 'Option 1' },
		{ value: 'option2', label: 'Option 2' },
		{ value: 'option3', label: 'Option 3' }
	];

	function handleValueChange(value) {
		selectedValue = value;
		data.value = value;
	}
</script>

<div class="select-node min-w-64">
	<Handle type="target" position={Position.Left} />
	
	<Card class="border-2 border-green-300 bg-green-50/50">
		<CardHeader class="pb-2">
			<CardTitle class="flex items-center gap-2 text-sm font-semibold text-green-800">
				<span class="text-lg">📋</span>
				{data.label || 'Select Option'}
			</CardTitle>
		</CardHeader>
		<CardContent class="pt-0">
			<div class="space-y-2">
				<Label class="text-xs text-green-700">Choose an option:</Label>
				<Select.Root bind:value={selectedValue} onValueChange={handleValueChange}>
					<Select.Trigger class="w-full text-sm">
						{selectedValue ? options.find(opt => opt.value === selectedValue)?.label || selectedValue : "Select an option..."}
					</Select.Trigger>
					<Select.Content>
						{#each options as option}
							<Select.Item value={option.value}>{option.label}</Select.Item>
						{/each}
					</Select.Content>
				</Select.Root>
				{#if selectedValue}
					<div class="text-xs text-green-600 bg-green-100 p-1 rounded">
						Selected: {options.find(opt => opt.value === selectedValue)?.label || selectedValue}
					</div>
				{/if}
			</div>
		</CardContent>
	</Card>
	
	<Handle type="source" position={Position.Right} />
</div>

<style>
	.select-node {
		font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
	}
</style>
