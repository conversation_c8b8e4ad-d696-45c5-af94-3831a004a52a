<script>
	import { <PERSON><PERSON>, Position } from '@xyflow/svelte';
	import { Card, CardContent, CardHeader, CardTitle } from '$lib/components/ui/card';
	import { Label } from '$lib/components/ui/label';
	import RangeSlider from 'svelte-range-slider-pips';

	let { data } = $props();
	let sliderValues = $state(data.values || [25, 75]);
	let singleValue = $state(data.value || 50);

	const isRange = data.range || false;
	const min = data.min || 0;
	const max = data.max || 100;
	const step = data.step || 1;

	function handleSliderChange(event) {
		if (isRange) {
			sliderValues = event.detail.values;
			data.values = sliderValues;
		} else {
			singleValue = event.detail.value;
			data.value = singleValue;
		}
	}
</script>

<div class="slider-node min-w-80">
	<Handle type="target" position={Position.Left} />
	
	<Card class="border-2 border-indigo-300 bg-indigo-50/50">
		<CardHeader class="pb-2">
			<CardTitle class="flex items-center gap-2 text-sm font-semibold text-indigo-800">
				<span class="text-lg">🎚️</span>
				{data.label || (isRange ? 'Range Slider' : 'Single Slider')}
			</CardTitle>
		</CardHeader>
		<CardContent class="pt-0">
			<div class="space-y-4">
				<Label class="text-xs text-indigo-700">
					{data.description || 'Adjust the value(s):'}
				</Label>
				
				<div class="px-2">
					{#if isRange}
						<RangeSlider
							bind:values={sliderValues}
							{min}
							{max}
							{step}
							range
							pushy
							pips
							pipstep={20}
							all="label"
							on:change={handleSliderChange}
							--slider="lightblue"
							--handle="indigo"
							--range="indigo"
							--pip="lightgray"
							--pip-text="gray"
						/>
					{:else}
						<RangeSlider
							bind:values={[singleValue]}
							{min}
							{max}
							{step}
							pips
							pipstep={20}
							all="label"
							on:change={handleSliderChange}
							--slider="lightblue"
							--handle="indigo"
							--range="indigo"
							--pip="lightgray"
							--pip-text="gray"
						/>
					{/if}
				</div>
				
				<div class="text-xs text-indigo-600 bg-indigo-100 p-2 rounded">
					{#if isRange}
						Range: {sliderValues[0]} - {sliderValues[1]}
					{:else}
						Value: {singleValue}
					{/if}
				</div>
			</div>
		</CardContent>
	</Card>
	
	<Handle type="source" position={Position.Right} />
</div>

<style>
	.slider-node {
		font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
	}
	
	:global(.slider-node .rangeSlider) {
		margin: 1rem 0;
	}
</style>
